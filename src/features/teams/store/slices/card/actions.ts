import { TeamKanbanStore, TeamKanbanCard, TeamKanbanSubtask } from '../../../types';
import { CardActions, CardState } from './types';
import { cardApi } from '../../../api/cardApi';
import { logger } from '@/utils/logger';
import { 
    commentOnCardTitleUpdate,
    commentOnCardDescriptionUpdate,
    commentOnCardDueDateUpdate,
    commentOnCardPriorityUpdate,
    commentOnCardAssigneeUpdate,
    commentOnCardCreation,
    commentOnCardMove,
    commentOnSubtaskCreation,
    commentOnSubtaskTitleUpdate,
    commentOnSubtaskCompletion,
    commentOnSubtaskDeletion,
    commentOnCardCompletion,
    commentOnCardDeletion
} from '../../../utils/autoComments';
import { calculateNewTimestamp } from '../../../utils/transformers';

type SetState = (
    partial: CardState | Partial<CardState> | ((state: CardState) => CardState | Partial<CardState>),
    replace?: boolean
) => void;

type GetState = () => TeamKanbanStore;

/**
 * Creates the actions for the card slice
 * Implements all card-related operations including fetching, adding, updating, and deleting cards and subtasks
 * 
 * @param set - Zustand's setState function
 * @param get - Zustand's getState function
 * @returns {CardActions} Object containing all card actions
 */
export function createCardActions(
    set: SetState,
    get: GetState
): CardActions {
    const DEFAULT_PAGE_SIZE = 10;

    return {
        /**
         * Fetches cards for a specific column with pagination support
         */
        fetchColumnCards: async (columnId: string, page = 1, limit = DEFAULT_PAGE_SIZE, showArchived = false) => {
            try {
                // Ensure showArchived is a boolean
                const showArchivedBool = Boolean(showArchived);

                console.log('Store fetchColumnCards CALLED with:', {
                    columnId,
                    page,
                    limit,
                    showArchived,
                    showArchivedBool,
                    typeOfShowArchived: typeof showArchived
                });

                set((state) => ({
                    loadingCards: { ...state.loadingCards, [columnId]: true }
                }));

                const { cards, count } = await cardApi.fetchCards(columnId, page, limit, showArchivedBool);
                console.log('Store fetchColumnCards GOT RESPONSE:', {
                    columnId,
                    cardsCount: cards.length,
                    totalCount: count,
                    showArchived,
                    cardsWithArchivedDate: cards.filter(c => c.archived_at !== null).length
                });

                if (count === undefined || count === null) throw new Error('Failed to fetch cards count');

                const state = get();

                // Update cards array with new cards
                const existingCards = new Set(state.cards.map((card: TeamKanbanCard) => card.id));
                const uniqueNewCards = cards.filter((card: TeamKanbanCard) => !existingCards.has(card.id));
                const updatedCards = [...state.cards, ...uniqueNewCards];

                // Update card pages for the column
                const existingPages = state.cardPages[columnId] || [];
                const updatedPages = page === 1
                    ? cards
                    : [...existingPages, ...cards];

                // Get existing column data
                const columnData = state.columnData.get(columnId) || {
                    cards: [],
                    loading: false,
                    error: null,
                    hasMore: true,
                    currentPage: 0
                };

                // Create new column data map
                const newColumnData = new Map(state.columnData);
                newColumnData.set(columnId, {
                    ...columnData,
                    cards: page === 1 ? cards : [...columnData.cards, ...cards.filter(card =>
                        !columnData.cards.some(existingCard => existingCard.id === card.id)
                    )],
                    loading: false,
                    error: null,
                    hasMore: (page === 1 ? cards.length : columnData.cards.length + cards.length) < count,
                    currentPage: page
                });

                // Update the state
                set({
                    cards: updatedCards,
                    cardPages: {
                        ...state.cardPages,
                        [columnId]: updatedPages
                    },
                    hasMoreCards: {
                        ...state.hasMoreCards,
                        [columnId]: updatedPages.length < count
                    },
                    loadingCards: {
                        ...state.loadingCards,
                        [columnId]: false
                    }
                });

                // Also update columnData (which belongs to BoardSlice)
                (set as (fn: (state: TeamKanbanStore) => Partial<TeamKanbanStore>) => void)(

                    (_state: TeamKanbanStore) => ({
                        columnData: newColumnData
                    })
                );

                console.log(`Updated columnData for column ${columnId}`, {
                    cardCount: newColumnData.get(columnId)?.cards.length,
                    page,
                    hasMore: newColumnData.get(columnId)?.hasMore
                });

                return { count };
            } catch (error) {
                const state = get() as CardState;
                set({
                    loadingCards: {
                        ...state.loadingCards,
                        [columnId]: false
                    }
                });
                throw error;
            }
        },

        /**
         * Adds a new card to a column
         */
        addCard: async (columnId: string, teamId: string, title: string, description = '') => {
            const store = get();
            const cardState = store as CardState;
            const columnCards = cardState.cardPages[columnId] || [];

            // Card creation with position_updated_at happens in the API layer
            const newCard = await cardApi.addCard(columnId, teamId, title, description);

            // Update the cards array and card pages
            set({
                cards: [newCard, ...cardState.cards],
                cardPages: {
                    ...cardState.cardPages,
                    [columnId]: [newCard, ...columnCards]
                }
            });

            // Now update the columnData in the root store
            // This is a cross-slice update
            const rootSet = set as (fn: (state: TeamKanbanStore) => Partial<TeamKanbanStore>) => void;
            rootSet((state: TeamKanbanStore) => {
                // Safely access columnData if it exists
                if (!state.columnData) return {};

                const currentColumnData = state.columnData.get(columnId);
                if (!currentColumnData) return {};

                // Create a new Map with the updated column data
                const newColumnData = new Map(state.columnData);
                newColumnData.set(columnId, {
                    ...currentColumnData,
                    cards: [newCard, ...currentColumnData.cards]
                });

                return {
                    columnData: newColumnData
                };
            });

            console.log('Added new card to column', {
                columnId,
                cardId: newCard.id
            });

            // Add auto-comment for card creation
            try {
                await commentOnCardCreation(newCard);
            } catch (error) {
                logger.error('Failed to create auto-comment for new card', { 
                    error,
                    cardId: newCard.id
                });
            }

            return;
        },

        /**
         * Updates an existing card
         */
        updateCard: async (cardId: string, updates: Partial<TeamKanbanCard>) => {
            const state = get();
            const previousCard = state.cards.find(c => c.id === cardId);
            
            if (!previousCard) {
                logger.error('Card not found for update:', { cardId, updates });
                throw new Error('Card not found');
            }

            // Store the previous assignee before any updates
            const _previousAssignee = previousCard.assignee;
            
            logger.debug('Starting card update:', {
                cardId,
                updates,
                _previousAssignee,
                previousCard
            });

            const isArchiveStatusChange = updates.archived_at !== undefined && 
                updates.archived_at !== previousCard.archived_at;

            try {
                // Create optimistic update
                const optimisticCard = { ...previousCard, ...updates };
                const columnId = previousCard.column_id;

                // Get current view state before any updates
                const isCurrentViewArchived = state.showArchivedColumns.has(columnId);
                const shouldRemoveFromCurrentView = isArchiveStatusChange && 
                    isCurrentViewArchived === !updates.archived_at;

                // Apply optimistic update to state
                set(state => {
                    // Start with current state
                    const newState: Partial<CardState> = {};

                    // Update cards array
                    newState.cards = state.cards.map(c =>
                        c.id === cardId ? optimisticCard : c
                    );

                    // Update column data
                    const newColumnData = new Map(state.columnData);
                    const columnData = newColumnData.get(columnId);
                    if (columnData) {
                        const updatedCards = shouldRemoveFromCurrentView
                            ? columnData.cards.filter(c => c.id !== cardId)
                            : columnData.cards.map(c => c.id === cardId ? optimisticCard : c);

                        newColumnData.set(columnId, {
                            ...columnData,
                            cards: updatedCards
                        });
                    }
                    newState.columnData = newColumnData;

                    // Update card pages
                    const cardPages = { ...state.cardPages };
                    Object.keys(cardPages).forEach(pageKey => {
                        if (shouldRemoveFromCurrentView) {
                            cardPages[pageKey] = cardPages[pageKey].filter(c => c.id !== cardId);
                        } else {
                            cardPages[pageKey] = cardPages[pageKey].map(c =>
                                c.id === cardId ? optimisticCard : c
                            );
                        }
                    });
                    newState.cardPages = cardPages;

                    return newState;
                });

                // Make API call
                const updatedCard = await cardApi.updateCard(cardId, updates);

                logger.debug('Card updated, checking for auto-comments:', {
                    _previousAssignee,
                    newAssignee: updates.assignee,
                    updatedCardAssignee: updatedCard.assignee,
                    hasAssigneeUpdate: 'assignee' in updates
                });

                // Generate auto-comments based on what changed
                try {
                    // Title update
                    if (updates.title !== undefined && updates.title !== previousCard.title) {
                        await commentOnCardTitleUpdate(updatedCard, previousCard.title);
                    }
                    
                    // Description update
                    if (updates.description !== undefined && updates.description !== previousCard.description) {
                        await commentOnCardDescriptionUpdate(updatedCard, previousCard.description, updates.description);
                    }

                    // Due date update
                    if (updates.due_date !== undefined && updates.due_date !== previousCard.due_date) {
                        await commentOnCardDueDateUpdate(updatedCard, previousCard.due_date);
                    }
                
                    // Priority update
                    if (updates.priority !== undefined && updates.priority !== previousCard.priority) {
                        await commentOnCardPriorityUpdate(updatedCard, previousCard.priority);
                    }

                    // Assignee update
                    if ('assignee' in updates) {
                        logger.debug('Processing assignee update:', {
                            _previousAssignee,
                            newAssignee: updates.assignee,
                            condition1: !_previousAssignee && updates.assignee,
                            condition2: _previousAssignee && !updates.assignee,
                            condition3: _previousAssignee && updates.assignee && _previousAssignee.id !== updates.assignee.id
                        });

                        // Create a card object with the new assignee for the comment
                        const cardForComment = {
                            ...updatedCard,
                            assignee: updates.assignee ?? null  // Ensure assignee is never undefined
                        };

                        if (!_previousAssignee && updates.assignee) {
                            // Case 1: No previous assignee, new assignee added
                            logger.debug('Case 1: Adding new assignee');
                            await commentOnCardAssigneeUpdate(cardForComment, null);
                        } else if (_previousAssignee && !updates.assignee) {
                            // Case 2: Had previous assignee, now unassigned
                            logger.debug('Case 2: Removing assignee');
                            await commentOnCardAssigneeUpdate(cardForComment, _previousAssignee);
                        } else if (_previousAssignee && updates.assignee && _previousAssignee.id !== updates.assignee.id) {
                            // Case 3: Changed from one assignee to another
                            logger.debug('Case 3: Changing assignee');
                            await commentOnCardAssigneeUpdate(cardForComment, _previousAssignee);
                        }
                    }
                    
                    // Archive status update
                    if (isArchiveStatusChange) {
                        await commentOnCardCompletion(updatedCard, !!previousCard.archived_at);
                    }
                } catch (error) {
                    logger.error('Failed to create auto-comments for card update', {
                        error,
                        cardId,
                        updates,
                        _previousAssignee
                    });
                }

                return updatedCard;

            } catch (error) {
                // On error, revert the optimistic update
                set(state => {
                    const columnId = previousCard.column_id;
                    const newState: Partial<CardState> = {};

                    // Revert cards array
                    newState.cards = state.cards.map(c =>
                        c.id === cardId ? previousCard : c
                    );

                    // Revert column data
                    const newColumnData = new Map(state.columnData);
                    const columnData = newColumnData.get(columnId);
                    if (columnData) {
                        newColumnData.set(columnId, {
                            ...columnData,
                            cards: columnData.cards.map(c =>
                                c.id === cardId ? previousCard : c
                            )
                        });
                    }
                    newState.columnData = newColumnData;

                    // Revert card pages
                    const cardPages = { ...state.cardPages };
                    Object.keys(cardPages).forEach(pageKey => {
                        cardPages[pageKey] = cardPages[pageKey].map(c =>
                            c.id === cardId ? previousCard : c
                        );
                    });
                    newState.cardPages = cardPages;

                    return newState;
                });

                logger.error('Failed to update card:', { 
                    error,
                    cardId,
                    updates
                });
                throw error;
            }
        },

        /**
         * Deletes a card
         */
        deleteCard: async (cardId: string) => {
            const state = get() as CardState;
            const card = state.cards.find(c => c.id === cardId);
            
            if (!card) {
                throw new Error(`Card with ID ${cardId} not found`);
            }
            
            // Create a copy of the card for the auto-comment before deleting
            const cardCopy = { ...card };
            
            // Delete the card from database
            await cardApi.deleteCard(cardId);

            // Remove card from cards array
            const cards = state.cards.filter(card => card.id !== cardId);

            // Remove card from card pages
            const cardPages = { ...state.cardPages };
            Object.keys(cardPages).forEach(columnId => {
                cardPages[columnId] = cardPages[columnId].filter(card => card.id !== cardId);
            });

            set({ cards, cardPages });
            
            // Add auto-comment for card deletion in the parent card or a system log
            try {
                await commentOnCardDeletion(cardCopy);
            } catch (error) {
                logger.error('Failed to create auto-comment for card deletion', {
                    error,
                    cardId
                });
            }
        },

        /**
         * Moves a card to a different column
         */
        moveCard: async (cardId: string, columnId: string, newIndex: number) => {
            const state = get() as CardState;
            const card = state.cards.find(c => c.id === cardId);

            if (!card) {
                throw new Error(`Card with ID ${cardId} not found`);
            }

            const sourceColumnId = card.column_id;
            const targetCards = state.cardPages[columnId] || [];

            // Use timestamp-based positioning instead of order_index
            const newPosition = calculateNewTimestamp(targetCards, newIndex);

            // Update in database
            await cardApi.moveCard(cardId, columnId, newPosition);

            // Create updated card with new column_id and position_updated_at
            const updatedCard: TeamKanbanCard = {
                ...card,
                column_id: columnId,
                position_updated_at: newPosition
            };

            // Update cards array in card slice
            set((state) => ({
                cards: state.cards.map(c => c.id === cardId ? updatedCard : c)
            }));

            // Update card pages in card slice
            set((state) => {
                const cardPages = { ...state.cardPages };

                // Remove from source column
                if (cardPages[sourceColumnId]) {
                    cardPages[sourceColumnId] = cardPages[sourceColumnId].filter(c => c.id !== cardId);
                }

                // Add to target column
                if (!cardPages[columnId]) {
                    cardPages[columnId] = [];
                }

                // Insert at the correct position
                const targetColumnCards = [...cardPages[columnId]];
                targetColumnCards.splice(newIndex, 0, updatedCard);
                cardPages[columnId] = targetColumnCards;

                return { cardPages };
            });

            // Update columnData in board slice - using a more specific type
            // This is necessary because we're accessing board slice state from the card slice
            type ColumnDataType = {
                cards: TeamKanbanCard[],
                loading: boolean,
                error: string | null,
                hasMore: boolean,
                currentPage: number
            };

            interface BoardSliceState {
                columnData: Map<string, ColumnDataType>;
            }

            (set as (fn: (state: TeamKanbanStore) => Partial<TeamKanbanStore>) => void)(
                (state: TeamKanbanStore & BoardSliceState) => {
                    const columnData = new Map(state.columnData);

                    // Update source column data
                    if (columnData.has(sourceColumnId)) {
                        const sourceData = columnData.get(sourceColumnId) as ColumnDataType;
                        if (sourceData) {
                            columnData.set(sourceColumnId, {
                                ...sourceData,
                                cards: sourceData.cards.filter((c: TeamKanbanCard) => c.id !== cardId)
                            });
                        }
                    }

                    // Update target column data
                    if (columnData.has(columnId)) {
                        const targetData = columnData.get(columnId) as ColumnDataType;
                        if (targetData) {
                            const updatedCards = [...targetData.cards];
                            // Remove card if it already exists in the target column
                            const existingIndex = updatedCards.findIndex((c: TeamKanbanCard) => c.id === cardId);
                            if (existingIndex !== -1) {
                                updatedCards.splice(existingIndex, 1);
                            }
                            // Insert at the new position
                            updatedCards.splice(newIndex, 0, updatedCard);

                            columnData.set(columnId, {
                                ...targetData,
                                cards: updatedCards
                            });
                        }
                    }

                    return { columnData };
                }
            );
            
            // Add auto-comment for card movement
            try {
                // Get the column data to provide context in the comment
                const sourceColumn = state.columns?.find(c => c.id === sourceColumnId);
                const targetColumn = state.columns?.find(c => c.id === columnId);
                
                if (sourceColumn && targetColumn) {
                    await commentOnCardMove(
                        updatedCard,
                        sourceColumn.title,
                        targetColumn.title
                    );
                }
            } catch (error) {
                logger.error('Failed to create auto-comment for card movement', { 
                    error,
                    cardId,
                    sourceColumnId,
                    targetColumnId: columnId
                });
            }
        },

        /**
         * Adds a subtask to a card
         */
        addSubtask: async (cardId: string, title: string) => {
            const state = get() as CardState;

            // Find the card to update
            const card = state.cards.find(c => c.id === cardId);
            if (!card) {
                throw new Error(`Card with ID ${cardId} not found`);
            }

            // Calculate the new order index
            const cardSubtasks = state.subtasks.filter(st => st.card_id === cardId);
            const lastOrderIndex = cardSubtasks.length > 0
                ? cardSubtasks[cardSubtasks.length - 1].order_index
                : 0;

            try {
                // Add subtask in database
                const newSubtask = await cardApi.addSubtask(cardId, title, lastOrderIndex + 1024);

                // Update subtasks array in card slice
                set((state: CardState) => ({
                    subtasks: [...state.subtasks, newSubtask]
                }));

                // Update cards array to include the new subtask
                set((state: CardState) => ({
                    cards: state.cards.map(c => {
                        if (c.id === cardId) {
                            return {
                                ...c,
                                subtasks: [...(c.subtasks || []), newSubtask]
                            };
                        }
                        return c;
                    })
                }));

                // Add auto-comment for subtask creation
                try {
                    await commentOnSubtaskCreation(cardId, newSubtask);
                } catch (error) {
                    logger.error('Failed to create auto-comment for subtask creation', {
                        error,
                        cardId,
                        subtaskId: newSubtask.id
                    });
                }
            } catch (error) {
                // Use properly typed error setter
                (set as (fn: (state: TeamKanbanStore) => Partial<TeamKanbanStore>) => void)(

                    (_state: TeamKanbanStore) => ({
                        error: error instanceof Error ? error.message : 'Failed to add subtask'
                    })
                );
                throw error;
            }
        },

        /**
         * Updates an existing subtask
         */
        updateSubtask: async (subtaskId: string, updates: Partial<TeamKanbanSubtask>) => {
            try {
                const state = get() as CardState;

                // Find the subtask to update
                const subtask = state.subtasks.find(st => st.id === subtaskId);
                if (!subtask) throw new Error('Subtask not found');

                // Keep a copy of the original subtask for auto-comments
                const previousSubtask = { ...subtask };

                // Extract assignee separately from updates
                const { assignee, ...dbUpdates } = updates;
                // Process assignee separately if needed
                if (assignee !== undefined) {
                    dbUpdates.assignee_id = assignee?.id ? assignee.id : undefined;
                }

                // Update subtask in database
                const updatedSubtask = await cardApi.updateSubtask(subtaskId, dbUpdates);

                // Update subtasks array in card slice
                set((state: CardState) => ({
                    subtasks: state.subtasks.map(st =>
                        st.id === subtaskId ? updatedSubtask : st
                    )
                }));

                // Update cards array to include the updated subtask
                set((state: CardState) => ({
                    cards: state.cards.map(c => {
                        if (c.id === subtask.card_id) {
                            return {
                                ...c,
                                subtasks: (c.subtasks || []).map(st =>
                                    st.id === subtaskId ? updatedSubtask : st
                                )
                            };
                        }
                        return c;
                    })
                }));

                // Generate auto-comments for relevant updates
                try {
                    // If title was updated
                    if (updates.title !== undefined && previousSubtask.title !== updates.title) {
                        await commentOnSubtaskTitleUpdate(subtask.card_id, updatedSubtask, previousSubtask.title);
                    }
                    
                    // If completion status was updated
                    if (updates.is_completed !== undefined && updates.is_completed !== previousSubtask.is_completed) {
                        await commentOnSubtaskCompletion(subtask.card_id, updatedSubtask, previousSubtask.is_completed);
                    }

                    if (updates.due_date !== undefined && updates.due_date !== previousSubtask.due_date) {
                        const store = get();
                        // eslint-disable-next-line @typescript-eslint/no-explicit-any
                        const anyStore = store as any;
                        
                        if (!previousSubtask.due_date && updates.due_date) {
                            await anyStore.addComment(
                                subtask.card_id,
                                `📅 Due date for subtask "${updatedSubtask.title}" was set to ${new Date(updates.due_date).toLocaleDateString()}.`,
                                true
                            );
                        } else if (previousSubtask.due_date && !updates.due_date) {
                            await anyStore.addComment(
                                subtask.card_id,
                                `📅 Due date for subtask "${updatedSubtask.title}" was removed.`,
                                true
                            );
                        } else if (previousSubtask.due_date && updates.due_date) {
                            await anyStore.addComment(
                                subtask.card_id,
                                `📅 Due date for subtask "${updatedSubtask.title}" was updated from ${new Date(previousSubtask.due_date).toLocaleDateString()} to ${new Date(updates.due_date).toLocaleDateString()}.`,
                                true
                            );
                        }
                    }
                    
                    if (updates.assignee_id !== undefined && updates.assignee_id !== previousSubtask.assignee_id) {
                        const store = get();
                        // eslint-disable-next-line @typescript-eslint/no-explicit-any
                        const anyStore = store as any;
                        
                        // Get user details from the store
                        const prevAssignee = previousSubtask.assignee;
                        const newAssignee = updatedSubtask.assignee;
                        
                        if (!prevAssignee && newAssignee) {
                            await anyStore.addComment(
                                subtask.card_id,
                                `👤 ${newAssignee.name} was assigned to subtask "${updatedSubtask.title}".`,
                                true
                            );
                        } else if (prevAssignee && !newAssignee) {
                            await anyStore.addComment(
                                subtask.card_id,
                                `👤 ${prevAssignee.name} was unassigned from subtask "${updatedSubtask.title}".`,
                                true
                            );
                        } else if (prevAssignee && newAssignee && prevAssignee.id !== newAssignee.id) {
                            await anyStore.addComment(
                                subtask.card_id,
                                `👤 Subtask "${updatedSubtask.title}" was reassigned from ${prevAssignee.name} to ${newAssignee.name}.`,
                                true
                            );
                        }
                    }

                } catch (error) {
                    logger.error('Failed to create auto-comment for subtask update', {
                        error,
                        subtaskId,
                        updates
                    });
                }

            } catch (error) {
                // Use properly typed error setter
                (set as (fn: (state: TeamKanbanStore) => Partial<TeamKanbanStore>) => void)(
                    (_state: TeamKanbanStore) => ({
                        error: error instanceof Error ? error.message : 'Failed to update subtask'
                    })
                );
                throw error;
            }
        },

        /**
         * Deletes a subtask
         */
        deleteSubtask: async (subtaskId: string) => {
            try {
                const state = get() as CardState;

                // Find the subtask to delete
                const subtask = state.subtasks.find(st => st.id === subtaskId);
                if (!subtask) throw new Error('Subtask not found');
                
                // Save subtask details for auto-comment before deletion
                const { card_id: cardId, title: subtaskTitle } = subtask;

                // Delete subtask from database
                await cardApi.deleteSubtask(subtaskId);

                // Update subtasks array in card slice
                set((state: CardState) => ({
                    subtasks: state.subtasks.filter(st => st.id !== subtaskId)
                }));

                // Update cards array to exclude the deleted subtask
                set((state: CardState) => ({
                    cards: state.cards.map(c => {
                        if (c.id === subtask.card_id) {
                            return {
                                ...c,
                                subtasks: (c.subtasks || []).filter(st => st.id !== subtaskId)
                            };
                        }
                        return c;
                    })
                }));

                // Generate auto-comment for subtask deletion
                try {
                    await commentOnSubtaskDeletion(cardId, subtaskTitle);
                } catch (error) {
                    logger.error('Failed to create auto-comment for subtask deletion', {
                        error,
                        subtaskId,
                        cardId
                    });
                }

                // Explicitly not returning anything to match void return type
            } catch (error) {
                // Use properly typed error setter
                (set as (fn: (state: TeamKanbanStore) => Partial<TeamKanbanStore>) => void)(
                    (_state: TeamKanbanStore) => ({
                        error: error instanceof Error ? error.message : 'Failed to delete subtask'
                    })
                );
                throw error;
            }
        },

        /**
         * Reorders subtasks within a card
         */
        reorderSubtasks: async (_cardId: string, subtaskIds: string[]) => {
            const updates = subtaskIds.map((id, index) => ({
                id,
                order_index: index * 1024
            }));

            await cardApi.reorderSubtasks(updates);
            const state = get() as CardState;

            set({
                subtasks: state.subtasks
                    .map(st => ({
                        ...st,
                        order_index: subtaskIds.indexOf(st.id) * 1024
                    }))
                    .sort((a, b) => a.order_index - b.order_index)
            });
        }
    };
}


