import { TeamKanbanCard, TeamKanbanSubtask, TeamKanbanColumn } from '../../../types';

export interface CardState {
    cards: TeamKanbanCard[];
    subtasks: TeamKanbanSubtask[];
    cardPages: Record<string, TeamKanbanCard[]>;
    hasMoreCards: Record<string, boolean>;
    loadingCards: Record<string, boolean>;
    columnData: Map<string, { cards: TeamKanbanCard[], loading: boolean, error: string | null, hasMore: boolean, currentPage: number }>;
    columns?: TeamKanbanColumn[];
}

export interface CardActions {
    fetchColumnCards: (columnId: string, page?: number, limit?: number, showArchived?: boolean) => Promise<{ count: number }>;
    addCard: (columnId: string, teamId: string, title: string, description?: string) => Promise<void>;
    updateCard: (cardId: string, updates: Partial<TeamKanbanCard>) => Promise<TeamKanbanCard>;
    deleteCard: (cardId: string) => Promise<void>;
    moveCard: (cardId: string, columnId: string, newIndex: number) => Promise<void>;
    addSubtask: (cardId: string, title: string) => Promise<void>;
    updateSubtask: (subtaskId: string, updates: Partial<TeamKanbanSubtask>) => Promise<void>;
    deleteSubtask: (subtaskId: string) => Promise<void>;
    reorderSubtasks: (cardId: string, subtaskIds: string[]) => Promise<void>;
}

export interface CardSlice extends CardState, CardActions { }